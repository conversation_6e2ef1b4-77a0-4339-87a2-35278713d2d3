{application,benchee,
             [{modules,['Elixir.Benchee','Elixir.Benchee.Benchmark',
                        'Elixir.Benchee.Benchmark.BenchmarkConfig',
                        'Elixir.Benchee.Benchmark.Collect',
                        'Elixir.Benchee.Benchmark.Collect.Memory',
                        'Elixir.Benchee.Benchmark.Collect.Reductions',
                        'Elixir.Benchee.Benchmark.Collect.Time',
                        'Elixir.Benchee.Benchmark.FunctionCallOverhead',
                        'Elixir.Benchee.Benchmark.Hooks',
                        'Elixir.Benchee.Benchmark.RepeatedMeasurement',
                        'Elixir.Benchee.Benchmark.Runner',
                        'Elixir.Benchee.Benchmark.ScenarioContext',
                        'Elixir.Benchee.CollectionData',
                        'Elixir.Benchee.Configuration',
                        'Elixir.Benchee.Conversion',
                        'Elixir.Benchee.Conversion.Count',
                        'Elixir.Benchee.Conversion.DeviationPercent',
                        'Elixir.Benchee.Conversion.Duration',
                        'Elixir.Benchee.Conversion.Format',
                        'Elixir.Benchee.Conversion.Memory',
                        'Elixir.Benchee.Conversion.Scale',
                        'Elixir.Benchee.Conversion.Unit',
                        'Elixir.Benchee.Formatter',
                        'Elixir.Benchee.Formatters.Console',
                        'Elixir.Benchee.Formatters.Console.Helpers',
                        'Elixir.Benchee.Formatters.Console.Memory',
                        'Elixir.Benchee.Formatters.Console.Reductions',
                        'Elixir.Benchee.Formatters.Console.RunTime',
                        'Elixir.Benchee.Formatters.TaggedSave',
                        'Elixir.Benchee.Output.BenchmarkPrinter',
                        'Elixir.Benchee.Output.ProfilePrinter',
                        'Elixir.Benchee.Output.ProgressPrinter',
                        'Elixir.Benchee.PreCheckError',
                        'Elixir.Benchee.Profile',
                        'Elixir.Benchee.RelativeStatistics',
                        'Elixir.Benchee.Scenario',
                        'Elixir.Benchee.ScenarioLoader',
                        'Elixir.Benchee.Statistics','Elixir.Benchee.Suite',
                        'Elixir.Benchee.System',
                        'Elixir.Benchee.UnknownProfilerError',
                        'Elixir.Benchee.Utility.DeepConvert',
                        'Elixir.Benchee.Utility.ErlangVersion',
                        'Elixir.Benchee.Utility.FileCreation',
                        'Elixir.Benchee.Utility.Parallel',
                        'Elixir.Benchee.Utility.RepeatN',
                        'Elixir.DeepMerge.Resolver.Benchee.Configuration',
                        'Elixir.DeepMerge.Resolver.Benchee.Suite',
                        'Elixir.DeepMerge.Resolver.Benchee.System',benchee]},
              {optional_applications,[table]},
              {applications,[kernel,stdlib,elixir,table,deep_merge,statistex]},
              {description,"Versatile (micro) benchmarking that is extensible. Get statistics such as:\naverage, iterations per second, standard deviation and the median.\n"},
              {registered,[]},
              {vsn,"1.4.0"}]}.
