{minimum_otp_vsn,"18.0"}.
{erl_opts,[debug_info,report,verbose,warn_deprecated_function,
           warn_deprecated_type,warn_export_all,warn_export_vars,
           warn_obsolete_guard,warn_untyped_record,warn_unused_import]}.
{xref_checks,[undefined_function_calls,undefined_functions,locals_not_used,
              deprecated_function_calls,deprecated_functions]}.
{cover_enabled,true}.
{cover_print_enabled,true}.
{cover_export_enabled,true}.
{alias,[{check,[xref,
                {eunit,"-c"},
                {cover,"-v --min_coverage=75"},
                dialyzer,edoc]}]}.
{profiles,[{test,[{extra_src_dirs,[{"test",[{recursive,true}]}]}]}]}.
{overrides,[]}.
