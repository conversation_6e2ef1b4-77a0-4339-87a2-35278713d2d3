{application,yamerl,
             [{description,"YAML 1.2 and JSON parser in pure Erlang"},
              {vsn,"0.10.0"},
              {modules,[yamerl,yamerl_app,yamerl_constr,yamerl_errors,
                        yamerl_node_binary,yamerl_node_bool,
                        yamerl_node_bool_ext,yamerl_node_bool_json,
                        yamerl_node_erlang_atom,yamerl_node_erlang_fun,
                        yamerl_node_float,yamerl_node_float_ext,
                        yamerl_node_float_json,yamerl_node_int,
                        yamerl_node_int_ext,yamerl_node_int_json,
                        yamerl_node_ipaddr,yamerl_node_map,yamerl_node_null,
                        yamerl_node_null_json,yamerl_node_seq,
                        yamerl_node_size,yamerl_node_str,yamerl_node_str_json,
                        yamerl_node_timestamp,yamerl_parser,yamerl_sup,
                        yamerl_yamler_compat]},
              {registered,[yamerl_sup]},
              {applications,[kernel,stdlib]},
              {mod,{yamerl_app,[]}},
              {env,[{node_mods,[]}]},
              {licenses,["BSD 2-Clause"]},
              {links,[{"GitHub","https://github.com/yakaz/yamerl"}]}]}.
