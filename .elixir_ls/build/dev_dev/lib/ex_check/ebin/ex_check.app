{application,ex_check,
             [{modules,['Elixir.ExCheck.Check',
                        'Elixir.ExCheck.Check.Compiler',
                        'Elixir.ExCheck.Check.Pipeline',
                        'Elixir.ExCheck.Command','Elixir.ExCheck.Config',
                        'Elixir.ExCheck.Config.Default',
                        'Elixir.ExCheck.Config.Generator',
                        'Elixir.ExCheck.Config.Loader',
                        'Elixir.ExCheck.Manifest','Elixir.ExCheck.Printer',
                        'Elixir.ExCheck.Project','Elixir.Mix.Tasks.Check',
                        'Elixir.Mix.Tasks.Check.Gen.Config']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"ex_check"},
              {registered,[]},
              {vsn,"0.16.0"}]}.
