{application,deep_merge,
             [{modules,['Elixir.DeepMerge','Elixir.DeepMerge.Resolver',
                        'Elixir.DeepMerge.Resolver.Any',
                        'Elixir.DeepMerge.Resolver.List',
                        'Elixir.DeepMerge.Resolver.Map']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"Deep (recursive) merging for maps, keyword lists and whatever else\nyou may want via implementing a simple protocol.\n"},
              {registered,[]},
              {vsn,"1.0.0"}]}.
