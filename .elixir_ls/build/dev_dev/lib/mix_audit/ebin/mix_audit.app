{application,mix_audit,
             [{modules,['Elixir.Jason.Encoder.MixAudit.Advisory',
                        'Elixir.Jason.Encoder.MixAudit.Dependency',
                        'Elixir.Jason.Encoder.MixAudit.Report',
                        'Elixir.Jason.Encoder.MixAudit.Vulnerability',
                        'Elixir.Mix.Tasks.Deps.Audit','Elixir.MixAudit',
                        'Elixir.MixAudit.Advisory','Elixir.MixAudit.Audit',
                        'Elixir.MixAudit.CLI','Elixir.MixAudit.CLI.Audit',
                        'Elixir.MixAudit.CLI.Help',
                        'Elixir.MixAudit.CLI.Version',
                        'Elixir.MixAudit.Dependency',
                        'Elixir.MixAudit.Formatting',
                        'Elixir.MixAudit.Formatting.Human',
                        'Elixir.MixAudit.Formatting.JSON',
                        'Elixir.MixAudit.Project','Elixir.MixAudit.Repo',
                        'Elixir.MixAudit.Report',
                        'Elixir.MixAudit.Vulnerability']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,yaml_elixir,jason]},
              {description,"MixAudit provides a `mix deps.audit` task to scan a project Mix dependencies for known Elixir security vulnerabilities"},
              {registered,[]},
              {vsn,"2.1.5"}]}.
