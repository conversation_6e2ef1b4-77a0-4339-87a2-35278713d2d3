{application,statistex,
             [{modules,['Elixir.Statistex','Elixir.Statistex.Helper',
                        'Elixir.Statistex.Mode',
                        'Elixir.Statistex.Percentile']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"Calculate statistics on data sets, reusing previously calculated values or just all metrics at once. Part of the benchee library family.\n"},
              {registered,[]},
              {vsn,"1.1.0"}]}.
