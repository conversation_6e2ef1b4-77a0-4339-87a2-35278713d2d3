{application,req,
             [{modules,['Elixir.Collectable.Req.Utils.CollectWithHash',
                        'Elixir.Enumerable.Req.Response.Async',
                        'Elixir.Inspect.Req.Request',
                        'Elixir.Inspect.Req.Response.Async','Elixir.Req',
                        'Elixir.Req.Application','Elixir.Req.ArchiveError',
                        'Elixir.Req.ChecksumMismatchError',
                        'Elixir.Req.DecompressError','Elixir.Req.Fields',
                        'Elixir.Req.Finch','Elixir.Req.HTTPError',
                        'Elixir.Req.Request','Elixir.Req.Response',
                        'Elixir.Req.Response.Async','Elixir.Req.Steps',
                        'Elixir.Req.Test','Elixir.Req.Test.Ownership',
                        'Elixir.Req.Test.OwnershipError',
                        'Elixir.Req.TooManyRedirectsError',
                        'Elixir.Req.TransportError','Elixir.Req.Utils',
                        'Elixir.Req.Utils.CollectWithHash']},
              {optional_applications,[nimble_csv,plug,brotli,ezstd]},
              {applications,[kernel,stdlib,elixir,logger,finch,mime,jason,
                             nimble_csv,plug,brotli,ezstd]},
              {description,"req"},
              {registered,[]},
              {vsn,"0.5.15"},
              {mod,{'Elixir.Req.Application',[]}}]}.
