{application,phx_new,
             [{modules,['Elixir.Mix.Tasks.Local.Phx',
                        'Elixir.Mix.Tasks.Phx.New',
                        'Elixir.Mix.Tasks.Phx.New.Ecto',
                        'Elixir.Mix.Tasks.Phx.New.Web','Elixir.Phx.New.Ecto',
                        'Elixir.Phx.New.Generator','Elixir.Phx.New.Mailer',
                        'Elixir.Phx.New.Project','Elixir.Phx.New.Single',
                        'Elixir.Phx.New.Umbrella','Elixir.Phx.New.Web']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,eex,crypto]},
              {description,"Phoenix framework project generator.\n\nProvides a `mix phx.new` task to bootstrap a new Elixir application\nwith Phoenix dependencies.\n"},
              {registered,[]},
              {vsn,"1.7.21"}]}.
