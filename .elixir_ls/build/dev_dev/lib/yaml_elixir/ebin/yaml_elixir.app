{application,yaml_elixir,
             [{modules,['Elixir.YamlElixir',
                        'Elixir.YamlElixir.FileNotFoundError',
                        'Elixir.YamlElixir.Mapper',
                        'Elixir.YamlElixir.Node.KeywordList',
                        'Elixir.YamlElixir.ParsingError',
                        'Elixir.YamlElixir.Records',
                        'Elixir.YamlElixir.Sigil']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,yamerl]},
              {description,"YAML parser for Elixir based on native Erlang implementation.\n"},
              {registered,[]},
              {vsn,"2.11.0"}]}.
