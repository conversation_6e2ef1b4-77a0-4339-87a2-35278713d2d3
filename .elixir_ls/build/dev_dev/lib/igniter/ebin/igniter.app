{application,igniter,
             [{modules,['Elixir.Igniter','Elixir.Igniter.CaptureServer',
                        'Elixir.Igniter.Code.Common',
                        'Elixir.Igniter.Code.Function',
                        'Elixir.Igniter.Code.Keyword',
                        'Elixir.Igniter.Code.List','Elixir.Igniter.Code.Map',
                        'Elixir.Igniter.Code.Module',
                        'Elixir.Igniter.Code.String',
                        'Elixir.Igniter.Code.Tuple',
                        'Elixir.Igniter.CopiedTasks',
                        'Elixir.Igniter.Extension',
                        'Elixir.Igniter.Extensions.Phoenix',
                        'Elixir.Igniter.Inflex','Elixir.Igniter.Libs.Ecto',
                        'Elixir.Igniter.Libs.Phoenix',
                        'Elixir.Igniter.Libs.Swoosh',
                        'Elixir.Igniter.Mix.Task',
                        'Elixir.Igniter.Mix.Task.Args',
                        'Elixir.Igniter.Mix.Task.Info',
                        'Elixir.Igniter.Phoenix.Generator',
                        'Elixir.Igniter.Phoenix.Single',
                        'Elixir.Igniter.Project.Application',
                        'Elixir.Igniter.Project.Config',
                        'Elixir.Igniter.Project.Deps',
                        'Elixir.Igniter.Project.Formatter',
                        'Elixir.Igniter.Project.IgniterConfig',
                        'Elixir.Igniter.Project.MixProject',
                        'Elixir.Igniter.Project.Module',
                        'Elixir.Igniter.Project.TaskAliases',
                        'Elixir.Igniter.Project.Test',
                        'Elixir.Igniter.Refactors.Elixir',
                        'Elixir.Igniter.Refactors.Rename',
                        'Elixir.Igniter.Rewrite.DotFormatterUpdater',
                        'Elixir.Igniter.Scribe','Elixir.Igniter.Test',
                        'Elixir.Igniter.Upgrades',
                        'Elixir.Igniter.Upgrades.Igniter',
                        'Elixir.Igniter.Util.BackwardsCompat',
                        'Elixir.Igniter.Util.Debug','Elixir.Igniter.Util.IO',
                        'Elixir.Igniter.Util.Info',
                        'Elixir.Igniter.Util.Install',
                        'Elixir.Igniter.Util.Loading',
                        'Elixir.Igniter.Util.Version',
                        'Elixir.Igniter.Util.Warning',
                        'Elixir.Inspect.Igniter',
                        'Elixir.Mix.Tasks.Igniter.Add',
                        'Elixir.Mix.Tasks.Igniter.AddExtension',
                        'Elixir.Mix.Tasks.Igniter.ApplyUpgrades',
                        'Elixir.Mix.Tasks.Igniter.Gen.Task',
                        'Elixir.Mix.Tasks.Igniter.Install',
                        'Elixir.Mix.Tasks.Igniter.MoveFiles',
                        'Elixir.Mix.Tasks.Igniter.Phx.Install',
                        'Elixir.Mix.Tasks.Igniter.Refactor.RenameFunction',
                        'Elixir.Mix.Tasks.Igniter.Refactor.UnlessToIfNot',
                        'Elixir.Mix.Tasks.Igniter.Remove',
                        'Elixir.Mix.Tasks.Igniter.Setup',
                        'Elixir.Mix.Tasks.Igniter.UpdateGettext',
                        'Elixir.Mix.Tasks.Igniter.Upgrade',
                        'Elixir.Mix.Tasks.Igniter.UpgradeIgniter']},
              {optional_applications,[phx_new]},
              {applications,[kernel,stdlib,elixir,logger,public_key,ssl,inets,
                             eex,rewrite,glob_ex,spitfire,sourceror,jason,req,
                             phx_new,owl,eflame,ex_check,git_ops,benchee,
                             doctor]},
              {description,"A code generation and project patching framework\n"},
              {registered,[]},
              {vsn,"0.6.25"}]}.
