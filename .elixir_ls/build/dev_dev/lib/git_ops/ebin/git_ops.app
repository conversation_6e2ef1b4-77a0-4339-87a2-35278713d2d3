{application,git_ops,
             [{modules,['Elixir.GitOps','Elixir.GitOps.Changelog',
                        'Elixir.GitOps.Commit','Elixir.GitOps.Config',
                        'Elixir.GitOps.Git','Elixir.GitOps.GitHub',
                        'Elixir.GitOps.Version',
                        'Elixir.GitOps.VersionReplace',
                        'Elixir.Mix.Tasks.GitOps.CheckMessage',
                        'Elixir.Mix.Tasks.GitOps.Install',
                        'Elixir.Mix.Tasks.GitOps.Install.Docs',
                        'Elixir.Mix.Tasks.GitOps.MessageHook',
                        'Elixir.Mix.Tasks.GitOps.ProjectInfo',
                        'Elixir.Mix.Tasks.GitOps.Release']},
              {compile_env,[{git_ops,['no_igniter?'],{ok,true}}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,git_cli,
                             nimble_parsec,req]},
              {description,"A tool for managing the version and changelog of a project using conventional commits.\n"},
              {registered,[]},
              {vsn,"2.8.0"}]}.
