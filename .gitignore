# The directory Mix will write compiled artifacts to.
/_build/

# If you run "mix test --cover", coverage assets end up here.
/cover/

# The directory Mix downloads your dependencies sources to.
/deps/

# Where third-party dependencies like ExDoc output generated docs.
/doc/

# Ignore .fetch files in case you like to edit your project deps locally.
/.fetch

# If the VM crashes, it generates a dump, let's ignore it too.
erl_crash.dump

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

# Ignore package tarball (built via "mix hex.build").
igniter-*.tar

# Temporary files, for example, from tests.
/tmp/

# The directory Mix will write compiled artifacts to.
/installer/_build/

# If you run "mix test --cover", coverage assets end up here.
/installer/cover/

# The directory Mix downloads your dependencies sources to.
/installer/deps/

# Where third-party dependencies like ExDoc output generated docs.
/installer/doc/

# Ignore .fetch files in case you like to edit your project deps locally.
/installer/.fetch

/installer/priv/docs

# If the VM crashes, it generates a dump, let's ignore it too.
installer/erl_crash.dump

/test_project

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

# Ignore package tarball (built via "mix hex.build").
igniter-*.tar

# Temporary files, for example, from tests.
installer/tmp/
