name: Test Projects
on:
  push:
    tags:
      - "v*"
    branches: [main]
jobs:
  test-projects:
    runs-on: ubuntu-latest
    name: ${{matrix.project.org}}/${{matrix.project.name}} - OTP ${{matrix.otp}} / Elixir ${{matrix.elixir}}
    strategy:
      fail-fast: false
      matrix:
        project:
          [
            {
              org: "ash-project",
              name: "ash",
              test-cmd: "mix test --only igniter",
            },
            {
              org: "team-alembic",
              name: "ash_authentication",
              test-cmd: "mix test --only igniter",
              postgres: true,
            },
            {
              org: "team-alembic",
              name: "ash_authentication_phoenix",
              test-cmd: "mix test --only igniter",
            },
            {
              org: "BeaconCMS",
              name: "beacon",
              test-cmd: "mix test --only igniter",
            },
            {
              org: "oban-bg",
              name: "oban",
              test-cmd: "mix test --only igniter",
            },
          ]
        otp: ["27.2"]
        elixir: ["1.18.1"]
    services:
      pg:
        image: ${{ (matrix.project.postgres) && 'postgres:16' || '' }}
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
        ports: ["5432:5432"]
    steps:
      - uses: actions/checkout@v2
        with:
          repository: ${{matrix.project.org}}/${{matrix.project.name}}
          path: ${{matrix.project.name}}
          ref: ${{matrix.project.ref}}
      - run: sudo apt-get install --yes libssl-dev
      - uses: actions/checkout@v2
        with:
          repository: ash-project/igniter
          path: igniter
      - uses: erlef/setup-beam@v1
        with:
          otp-version: ${{matrix.otp}}
          elixir-version: ${{matrix.elixir}}
      - uses: actions/cache@v4
        id: cache-deps
        with:
          path: ${{matrix.project.name}}/deps
          key: ${{matrix.project.name}}-otp-${{matrix.otp}}-elixir-${{matrix.elixir}}-deps-2-${{ hashFiles('config/**/*.exs') }}-${{ hashFiles(format('{0}{1}', github.workspace, '/ash/mix.lock')) }}
          restore-keys: ${{matrix.project.name}}-otp-${{matrix.otp}}-elixir-${{matrix.elixir}}-deps-2-${{ hashFiles('config/**/*.exs') }}-
      - uses: actions/cache@v4
        id: cache-build
        with:
          path: ${{matrix.project.name}}/_build
          key: ${{matrix.project.name}}-otp-${{matrix.otp}}-elixir-${{matrix.elixir}}-build-3-${{ hashFiles('config/**/*.exs') }}-${{ hashFiles(format('{0}{1}', github.workspace, '/ash/mix.lock')) }}
          restore-keys: ${{matrix.project.name}}-otp-${{matrix.otp}}-elixir-${{matrix.elixir}}-build-3-${{ hashFiles('config/**/*.exs') }}-
      - run: mix deps.get
        working-directory: ./${{matrix.project.name}}
      - run: mix archive.install hex igniter_new --force
      - run: mix archive.install hex phx_new --force
      - run: mix deps.update igniter
        working-directory: ./${{matrix.project.name}}
      - run: mix igniter.add igniter@path:../igniter --yes
        working-directory: ./${{matrix.project.name}}
      - run: mix deps.get
        working-directory: ./${{matrix.project.name}}
      - run: ${{matrix.project.test-cmd}}
        if: ${{matrix.project.test-cmd}}
        working-directory: ./${{matrix.project.name}}
