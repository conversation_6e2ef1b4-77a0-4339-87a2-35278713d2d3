name: CI
on:
  push:
    tags:
      - "v*"
    branches: [main]
  pull_request:
    branches: [main]
  workflow_call:
jobs:
  ash-ci:
    strategy:
      fail-fast: false
      matrix:
        include:
          - elixir-version: "1.16.3-otp-26"
            erlang-version: "26.0"
            primary: false
          - elixir-version: "default"
            erlang-version: "default"
            primary: true
    uses: ash-project/ash/.github/workflows/ash-ci.yml@main
    with:
      spark-formatter: false
      spark-cheat-sheets: false
      sobelow: false
      elixir-version: ${{ matrix.elixir-version }}
      erlang-version: ${{ matrix.erlang-version }}
      igniter-upgrade: false
      publish-docs: ${{ matrix.primary }}
      release: ${{ matrix.primary }}
    secrets:
      HEX_API_KEY: ${{ secrets.HEX_API_KEY }}
